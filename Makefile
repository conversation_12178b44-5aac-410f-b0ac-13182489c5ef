# Makefile for Raylib Tic-Tac-Toe Game

# Compiler settings
CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2
INCLUDES = -Iinclude

# Raylib settings (adjust paths as needed)
RAYLIB_PATH = /usr/local
RAYLIB_INCLUDE = -I$(RAY<PERSON>IB_PATH)/include
RAYLIB_LIB = -L$(RAYLIB_PATH)/lib -lraylib

# System libraries
LIBS = -lGL -lm -lpthread -ldl -lrt -lX11

# Source files
SRCDIR = src
SOURCES = $(wildcard $(SRCDIR)/*.cpp)
OBJECTS = $(SOURCES:.cpp=.o)

# Target executable
TARGET = TicTacToe

# Default target
all: $(TARGET)

# Build target
$(TARGET): $(OBJECTS)
	$(CXX) $(OBJECTS) -o $(TARGET) $(RAYLIB_LIB) $(LIBS)

# Compile source files
%.o: %.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) $(RAYLIB_INCLUDE) -c $< -o $@

# Clean build files
clean:
	rm -f $(OBJECTS) $(TARGET)

# Install raylib (Ubuntu/Debian)
install-raylib-apt:
	sudo apt update
	sudo apt install libraylib-dev

# Install raylib (Arch Linux)
install-raylib-pacman:
	sudo pacman -S raylib

# Install raylib from source
install-raylib-source:
	git clone https://github.com/raysan5/raylib.git
	cd raylib/src && make PLATFORM=PLATFORM_DESKTOP
	sudo make install RAYLIB_INSTALL_PATH=/usr/local

# Run the game
run: $(TARGET)
	./$(TARGET)

# Help
help:
	@echo "Available targets:"
	@echo "  all                 - Build the game"
	@echo "  clean               - Clean build files"
	@echo "  run                 - Build and run the game"
	@echo "  install-raylib-apt  - Install raylib on Ubuntu/Debian"
	@echo "  install-raylib-pacman - Install raylib on Arch Linux"
	@echo "  install-raylib-source - Install raylib from source"
	@echo "  help                - Show this help"

.PHONY: all clean run install-raylib-apt install-raylib-pacman install-raylib-source help
