#pragma once
#include "raylib.h"
#include <vector>
#include <functional>

enum class EaseType {
    LINEAR,
    EASE_IN,
    EASE_OUT,
    EASE_IN_OUT,
    BOUNCE,
    ELASTIC
};

struct AnimationData {
    float startValue;
    float endValue;
    float currentValue;
    float duration;
    float elapsed;
    EaseType easeType;
    std::function<void(float)> onUpdate;
    std::function<void()> onComplete;
    bool isActive;
    bool loop;
    
    AnimationData() : startValue(0), endValue(0), currentValue(0), duration(0), 
                     elapsed(0), easeType(EaseType::LINEAR), isActive(false), loop(false) {}
};

class Animation {
private:
    std::vector<AnimationData> animations;
    
    float ApplyEasing(float t, EaseType easeType);
    
public:
    Animation();
    
    void Update(float deltaTime);
    
    int StartAnimation(float startValue, float endValue, float duration, 
                      EaseType easeType, std::function<void(float)> onUpdate,
                      std::function<void()> onComplete = nullptr, bool loop = false);
    
    void StopAnimation(int id);
    void StopAllAnimations();
    
    bool IsAnimationActive(int id);
    int GetActiveAnimationCount();
    
    // Predefined animations
    int FadeIn(float duration, std::function<void(float)> onUpdate, std::function<void()> onComplete = nullptr);
    int FadeOut(float duration, std::function<void(float)> onUpdate, std::function<void()> onComplete = nullptr);
    int ScaleUp(float duration, std::function<void(float)> onUpdate, std::function<void()> onComplete = nullptr);
    int ScaleDown(float duration, std::function<void(float)> onUpdate, std::function<void()> onComplete = nullptr);
    int SlideIn(float startX, float endX, float duration, std::function<void(float)> onUpdate, std::function<void()> onComplete = nullptr);
};
