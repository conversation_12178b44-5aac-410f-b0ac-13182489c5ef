#pragma once
#include "GameState.h"
#include <random>

enum class BotDifficulty {
    EASY,
    MEDIUM,
    HARD,
    IMPOSSIBLE
};

class Bot {
private:
    BotDifficulty difficulty;
    std::mt19937 rng;
    Player botPlayer;
    
    int Minimax(GameState& state, int depth, bool isMaximizing, int alpha, int beta);
    int EvaluateBoard(const GameState& state);
    Move GetRandomMove(const GameState& state);
    Move GetEasyMove(const GameState& state);
    Move GetMediumMove(const GameState& state);
    Move GetHardMove(const GameState& state);
    Move GetImpossibleMove(const GameState& state);
    
public:
    Bot();
    
    void SetDifficulty(BotDifficulty diff) { difficulty = diff; }
    void SetPlayer(Player player) { botPlayer = player; }
    
    BotDifficulty GetDifficulty() const { return difficulty; }
    Player GetPlayer() const { return botPlayer; }
    
    Move GetBestMove(const GameState& state);
    std::string GetDifficultyName() const;
    
    static std::vector<std::string> GetDifficultyNames();
};
