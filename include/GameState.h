#pragma once
#include "raylib.h"
#include <array>
#include <vector>

enum class Player {
    NONE = 0,
    X = 1,
    O = 2
};

enum class GameResult {
    ONGOING,
    X_WINS,
    O_WINS,
    DRAW
};

struct Move {
    int row, col;
    Player player;
    
    Move(int r = -1, int c = -1, Player p = Player::NONE) 
        : row(r), col(c), player(p) {}
};

class GameState {
private:
    std::array<std::array<Player, 3>, 3> board;
    Player currentPlayer;
    GameResult result;
    std::vector<Move> moveHistory;
    std::vector<Vector2> winningLine;
    
    bool CheckWin(Player player);
    bool CheckDraw();
    void UpdateGameResult();
    
public:
    GameState();
    
    void Reset();
    bool MakeMove(int row, int col);
    bool UndoMove();
    
    Player GetCell(int row, int col) const;
    Player GetCurrentPlayer() const { return currentPlayer; }
    GameResult GetResult() const { return result; }
    
    bool IsValidMove(int row, int col) const;
    bool IsGameOver() const;
    
    std::vector<Move> GetAvailableMoves() const;
    std::vector<Move> GetMoveHistory() const { return moveHistory; }
    std::vector<Vector2> GetWinningLine() const { return winningLine; }
    
    // For bot evaluation
    GameState GetCopy() const;
    void SetBoard(const std::array<std::array<Player, 3>, 3>& newBoard);
    void SetCurrentPlayer(Player player) { currentPlayer = player; }
};
