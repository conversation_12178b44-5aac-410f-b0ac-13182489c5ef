#pragma once
#include "raylib.h"
#include "ThemeManager.h"
#include <string>
#include <functional>

struct <PERSON><PERSON> {
    Rectangle bounds;
    std::string text;
    std::function<void()> onClick;
    bool isHovered;
    bool isPressed;
    bool isEnabled;
    
    <PERSON><PERSON>(Rectangle rect, const std::string& txt, std::function<void()> callback)
        : bounds(rect), text(txt), onClick(callback), isHovered(false), 
          isPressed(false), isEnabled(true) {}
};

class UI {
private:
    std::vector<Button> buttons;
    Font font;
    bool fontLoaded;
    
    void UpdateButton(Button& button, Vector2 mousePos, bool mousePressed);
    
public:
    UI();
    ~UI();
    
    void Initialize();
    void Update(Vector2 mousePos, bool mousePressed);
    void ClearButtons();
    
    void AddButton(Rectangle bounds, const std::string& text, std::function<void()> onClick);
    void DrawButton(const Button& button, const Theme& theme);
    void DrawAllButtons(const Theme& theme);
    
    void DrawText(const std::string& text, Vector2 position, int fontSize, Color color);
    void DrawTextCentered(const std::string& text, Vector2 center, int fontSize, Color color);
    
    Vector2 MeasureText(const std::string& text, int fontSize);
    
    // Game board drawing
    void DrawBoard(Vector2 position, Vector2 size, const Theme& theme);
    void DrawCell(Vector2 position, Vector2 size, Player player, const Theme& theme, float alpha = 1.0f);
    void DrawWinLine(const std::vector<Vector2>& winningLine, Vector2 boardPos, Vector2 boardSize, const Theme& theme);
    
    // Utility functions
    Rectangle GetCellBounds(int row, int col, Vector2 boardPos, Vector2 boardSize);
    Vector2 GetCellFromPosition(Vector2 mousePos, Vector2 boardPos, Vector2 boardSize);
};
