#pragma once
#include "raylib.h"
#include "GameState.h"
#include "ThemeManager.h"
#include "Bot.h"
#include "Tutorial.h"
#include "UI.h"
#include "Animation.h"
#include <memory>

enum class GameMode {
    MENU,
    PLAYER_VS_PLAYER,
    PLAYER_VS_BOT,
    TUTORIAL,
    SETTINGS
};

class Game {
private:
    static const int SCREEN_WIDTH = 800;
    static const int SCREEN_HEIGHT = 600;
    
    GameMode currentMode;
    std::unique_ptr<GameState> gameState;
    std::unique_ptr<ThemeManager> themeManager;
    std::unique_ptr<Bot> bot;
    std::unique_ptr<Tutorial> tutorial;
    std::unique_ptr<UI> ui;
    std::unique_ptr<Animation> animation;
    
    bool isRunning;
    float deltaTime;
    
    void HandleInput();
    void Update();
    void Draw();
    void DrawMenu();
    void DrawGame();
    void DrawSettings();
    
public:
    Game();
    ~Game();
    
    void Initialize();
    void Run();
    void Shutdown();
    
    void SetGameMode(GameMode mode);
    GameMode GetGameMode() const { return currentMode; }
    
    ThemeManager* GetThemeManager() { return themeManager.get(); }
    GameState* GetGameState() { return gameState.get(); }
    Bot* GetBot() { return bot.get(); }
    Tutorial* GetTutorial() { return tutorial.get(); }
    UI* GetUI() { return ui.get(); }
    Animation* GetAnimation() { return animation.get(); }
};
