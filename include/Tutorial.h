#pragma once
#include "raylib.h"
#include "GameState.h"
#include <string>
#include <vector>

enum class TutorialStep {
    WELCOME,
    BOARD_EXPLANATION,
    HOW_TO_PLAY,
    WINNING_CONDITIONS,
    STRATEGY_BASICS,
    PRACTICE_GAME,
    COMPLETED
};

struct TutorialMessage {
    std::string title;
    std::string content;
    Vector2 highlightPos;
    Vector2 highlightSize;
    bool showHighlight;
};

class Tutorial {
private:
    TutorialStep currentStep;
    std::vector<TutorialMessage> messages;
    int currentMessageIndex;
    bool isActive;
    bool waitingForInput;
    GameState practiceGame;
    
    void InitializeMessages();
    void SetupPracticeGame();
    
public:
    Tutorial();
    
    void Start();
    void Stop();
    void Reset();
    
    void NextStep();
    void PreviousStep();
    
    void Update(float deltaTime);
    void HandleClick(Vector2 mousePos);
    
    bool IsActive() const { return isActive; }
    bool IsWaitingForInput() const { return waitingForInput; }
    
    TutorialStep GetCurrentStep() const { return currentStep; }
    TutorialMessage GetCurrentMessage() const;
    
    GameState* GetPracticeGame() { return &practiceGame; }
    
    void Draw(Vector2 boardPos, Vector2 boardSize);
};
