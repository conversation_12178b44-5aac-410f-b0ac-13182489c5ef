#pragma once
#include "raylib.h"
#include <string>
#include <vector>

struct Theme {
    std::string name;
    Color background;
    Color boardColor;
    Color gridLines;
    Color playerX;
    Color playerO;
    Color highlight;
    Color text;
    Color buttonNormal;
    Color buttonHover;
    Color buttonPressed;
    Color accent;
};

class ThemeManager {
private:
    std::vector<Theme> themes;
    int currentThemeIndex;
    float transitionProgress;
    bool isTransitioning;
    Theme transitionFrom;
    Theme transitionTo;
    
    void InitializeThemes();
    Color LerpColor(Color from, Color to, float t);
    
public:
    ThemeManager();
    
    void Update(float deltaTime);
    
    void SetTheme(int index);
    void NextTheme();
    void PreviousTheme();
    
    Theme GetCurrentTheme();
    int GetCurrentThemeIndex() const { return currentThemeIndex; }
    int GetThemeCount() const { return static_cast<int>(themes.size()); }
    std::string GetCurrentThemeName();
    std::vector<std::string> GetThemeNames();
    
    bool IsTransitioning() const { return isTransitioning; }
};
