#!/bin/bash

# Build script for Raylib Tic-<PERSON>c-Toe Game

set -e  # Exit on any error

echo "🎮 Building Raylib Tic-Tac-Toe Game..."

# Check if raylib is installed
check_raylib() {
    if ! pkg-config --exists raylib 2>/dev/null; then
        echo "❌ Raylib not found. Please install raylib first."
        echo ""
        echo "Installation options:"
        echo "  Ubuntu/Debian: sudo apt install libraylib-dev"
        echo "  Arch Linux:    sudo pacman -S raylib"
        echo "  From source:   make install-raylib-source"
        exit 1
    fi
    echo "✅ Raylib found"
}

# Check if compiler is available
check_compiler() {
    if ! command -v g++ &> /dev/null; then
        echo "❌ g++ compiler not found. Please install build tools."
        echo ""
        echo "Installation:"
        echo "  Ubuntu/Debian: sudo apt install build-essential"
        echo "  Arch Linux:    sudo pacman -S gcc make"
        exit 1
    fi
    echo "✅ g++ compiler found"
}

# Build using make
build_with_make() {
    echo "🔨 Building with Make..."
    make clean 2>/dev/null || true
    make
    echo "✅ Build completed successfully!"
}

# Build using cmake if available
build_with_cmake() {
    if command -v cmake &> /dev/null; then
        echo "🔨 Building with CMake..."
        mkdir -p build
        cd build
        cmake ..
        make
        cd ..
        echo "✅ Build completed successfully!"
        echo "📁 Executable: build/RaylibTicTacToe"
    else
        build_with_make
    fi
}

# Main build process
main() {
    check_compiler
    check_raylib
    
    # Try CMake first, fallback to Make
    if command -v cmake &> /dev/null; then
        build_with_cmake
    else
        build_with_make
    fi
    
    echo ""
    echo "🎉 Game built successfully!"
    echo ""
    echo "To run the game:"
    if [ -f "build/RaylibTicTacToe" ]; then
        echo "  ./build/RaylibTicTacToe"
    elif [ -f "TicTacToe" ]; then
        echo "  ./TicTacToe"
    else
        echo "  Check the build output above for the executable location"
    fi
    echo ""
    echo "Or use: make run"
}

# Run main function
main "$@"
