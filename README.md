# Raylib Tic-Tac-Toe Game

A modern, feature-rich Tic-Tac-Toe game built with C++ and Raylib, featuring smooth animations, multiple themes, AI opponent, and an interactive tutorial system.

## Features

### 🎮 Game Modes
- **Player vs Player** - Classic two-player mode
- **Player vs Bot** - Challenge an AI opponent with multiple difficulty levels
- **Tutorial Mode** - Interactive tutorial to learn the game rules and strategies

### 🎨 Visual Themes
Choose from 6 beautiful themes:
1. **Classic Blue** - Traditional blue color scheme
2. **Forest Green** - Nature-inspired green theme
3. **Sunset Orange** - Warm orange and pink colors
4. **Ocean Deep** - Deep blue ocean theme
5. **Purple Neon** - Vibrant purple and magenta
6. **Monochrome** - Classic black and white

### 🤖 AI Opponent
- **Easy** - 70% random moves, good for beginners
- **Medium** - 40% random moves, balanced challenge
- **Hard** - 15% random moves, challenging gameplay
- **Impossible** - Perfect minimax algorithm, unbeatable

### ✨ Features
- Smooth theme transitions with easing animations
- Interactive tutorial system
- Polished UI with hover effects
- Winning line highlighting
- Keyboard shortcuts (R to restart, ESC for menu)
- Clean, modern interface

## Building the Game

### Prerequisites
- C++17 compatible compiler (GCC, Clang, or MSVC)
- Raylib library
- Make (for Makefile build) or CMake

### Option 1: Using Makefile

1. **Install Raylib:**
   ```bash
   # On Ubuntu/Debian:
   make install-raylib-apt
   
   # On Arch Linux:
   make install-raylib-pacman
   
   # From source (any Linux):
   make install-raylib-source
   ```

2. **Build and run:**
   ```bash
   make run
   ```

### Option 2: Using CMake

1. **Install dependencies:**
   ```bash
   # Install CMake and build tools
   sudo apt install cmake build-essential  # Ubuntu/Debian
   sudo pacman -S cmake gcc make          # Arch Linux
   ```

2. **Build:**
   ```bash
   mkdir build
   cd build
   cmake ..
   make
   ./RaylibTicTacToe
   ```

## How to Play

### Basic Rules
1. The game is played on a 3×3 grid
2. Player X goes first
3. Players take turns placing their marks (X or O) in empty cells
4. The first player to get 3 marks in a row (horizontally, vertically, or diagonally) wins
5. If all 9 cells are filled and no one has 3 in a row, it's a draw

### Controls
- **Mouse**: Click on empty cells to make moves
- **R**: Restart current game
- **ESC**: Return to main menu
- **Tutorial Mode**: Follow on-screen instructions

### Strategy Tips
1. Try to get three in a row
2. Block your opponent from getting three in a row
3. The center square is often a good first move
4. Corner squares are strong positions
5. Think ahead about your opponent's next move

## Game Architecture

The game is built with a modular architecture:

- **Game.cpp** - Main game loop and state management
- **GameState.cpp** - Game logic and board state
- **ThemeManager.cpp** - Theme system with smooth transitions
- **Bot.cpp** - AI opponent with minimax algorithm
- **Tutorial.cpp** - Interactive tutorial system
- **UI.cpp** - User interface and rendering
- **Animation.cpp** - Animation system with easing functions

## Customization

### Adding New Themes
Edit `src/ThemeManager.cpp` and add new theme definitions in the `InitializeThemes()` function.

### Adjusting AI Difficulty
Modify the probability values in `src/Bot.cpp` for different difficulty levels.

### Changing Animations
Customize easing functions and animation durations in `src/Animation.cpp`.

## License

This project is open source. Feel free to modify and distribute.

## Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.
