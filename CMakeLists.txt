cmake_minimum_required(VERSION 3.16)
project(RaylibTicTacToe)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find raylib
find_package(raylib QUIET)
if (NOT raylib_FOUND)
    include(FetchContent)
    FetchContent_Declare(
        raylib
        GIT_REPOSITORY https://github.com/raysan5/raylib.git
        GIT_TAG 5.0
    )
    FetchContent_MakeAvailable(raylib)
endif()

# Add executable
add_executable(${PROJECT_NAME} 
    src/main.cpp
    src/Game.cpp
    src/GameState.cpp
    src/ThemeManager.cpp
    src/Bot.cpp
    src/Tutorial.cpp
    src/UI.cpp
    src/Animation.cpp
)

# Include directories
target_include_directories(${PROJECT_NAME} PRIVATE include)

# Link libraries
target_link_libraries(${PROJECT_NAME} raylib)

# Copy resources
file(COPY resources DESTINATION ${CMAKE_BINARY_DIR})

# Platform specific settings
if(WIN32)
    target_link_libraries(${PROJECT_NAME} winmm)
endif()

if(APPLE)
    target_link_libraries(${PROJECT_NAME} "-framework IOKit" "-framework Cocoa" "-framework OpenGL")
endif()
