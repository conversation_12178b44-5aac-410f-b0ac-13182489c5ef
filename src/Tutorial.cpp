#include "Tutorial.h"
#include "raylib.h"

Tutorial::Tutorial() : currentStep(TutorialStep::WELCOME), currentMessageIndex(0), 
                      isActive(false), waitingForInput(false) {
    InitializeMessages();
}

void Tutorial::InitializeMessages() {
    messages.clear();
    
    // Welcome message
    messages.push_back({
        "Welcome to Tic-Tac-Toe!",
        "This tutorial will teach you how to play Tic-Tac-Toe.\n\n"
        "Tic-Tac-Toe is a simple strategy game played on a 3x3 grid.\n"
        "Two players take turns placing their marks (X and O) on the board.\n\n"
        "Click anywhere to continue...",
        {0, 0}, {0, 0}, false
    });
    
    // Board explanation
    messages.push_back({
        "The Game Board",
        "The game is played on a 3x3 grid like this one.\n\n"
        "Each cell can hold either an X, an O, or be empty.\n"
        "Players take turns clicking on empty cells to place their mark.\n\n"
        "The highlighted area shows the game board.\n\n"
        "Click to continue...",
        {0, 0}, {300, 300}, true
    });
    
    // How to play
    messages.push_back({
        "How to Play",
        "Player X always goes first.\n"
        "Players alternate turns placing their marks.\n\n"
        "To make a move:\n"
        "1. Click on an empty cell\n"
        "2. Your mark will appear in that cell\n"
        "3. The turn passes to the other player\n\n"
        "Click to continue...",
        {0, 0}, {0, 0}, false
    });
    
    // Winning conditions
    messages.push_back({
        "How to Win",
        "To win, you need to get three of your marks in a row.\n\n"
        "You can win by getting three in a row:\n"
        "• Horizontally (across)\n"
        "• Vertically (up and down)\n"
        "• Diagonally (corner to corner)\n\n"
        "If all 9 cells are filled and no one has three in a row,\n"
        "the game is a draw (tie).\n\n"
        "Click to continue...",
        {0, 0}, {0, 0}, false
    });
    
    // Strategy basics
    messages.push_back({
        "Basic Strategy",
        "Here are some tips to improve your game:\n\n"
        "1. Try to get three in a row\n"
        "2. Block your opponent from getting three in a row\n"
        "3. The center square is often a good first move\n"
        "4. Corner squares are also strong positions\n"
        "5. Think ahead - what will happen after your move?\n\n"
        "Click to continue...",
        {0, 0}, {0, 0}, false
    });
    
    // Practice game
    messages.push_back({
        "Practice Time!",
        "Now let's practice! Try to win this game.\n\n"
        "You are X, and you go first.\n"
        "Click on any empty cell to make your move.\n\n"
        "The computer will play as O.\n"
        "Try to get three X's in a row!\n\n"
        "Good luck!",
        {0, 0}, {0, 0}, false
    });
    
    // Completed
    messages.push_back({
        "Tutorial Complete!",
        "Congratulations! You've completed the tutorial.\n\n"
        "You now know how to play Tic-Tac-Toe!\n\n"
        "Key points to remember:\n"
        "• Get three in a row to win\n"
        "• Block your opponent\n"
        "• Think strategically\n\n"
        "Press ESC to return to the main menu\n"
        "or continue practicing!",
        {0, 0}, {0, 0}, false
    });
}

void Tutorial::Start() {
    isActive = true;
    currentStep = TutorialStep::WELCOME;
    currentMessageIndex = 0;
    waitingForInput = true;
    SetupPracticeGame();
}

void Tutorial::Stop() {
    isActive = false;
    waitingForInput = false;
}

void Tutorial::Reset() {
    currentStep = TutorialStep::WELCOME;
    currentMessageIndex = 0;
    waitingForInput = true;
    SetupPracticeGame();
}

void Tutorial::NextStep() {
    if (currentStep == TutorialStep::COMPLETED) {
        return;
    }
    
    currentStep = static_cast<TutorialStep>(static_cast<int>(currentStep) + 1);
    currentMessageIndex = static_cast<int>(currentStep);
    
    if (currentStep == TutorialStep::PRACTICE_GAME) {
        SetupPracticeGame();
        waitingForInput = false;
    } else {
        waitingForInput = true;
    }
}

void Tutorial::PreviousStep() {
    if (currentStep == TutorialStep::WELCOME) {
        return;
    }
    
    currentStep = static_cast<TutorialStep>(static_cast<int>(currentStep) - 1);
    currentMessageIndex = static_cast<int>(currentStep);
    waitingForInput = true;
}

void Tutorial::Update(float deltaTime) {
    if (!isActive) return;
    
    // Check if practice game is completed
    if (currentStep == TutorialStep::PRACTICE_GAME && practiceGame.IsGameOver()) {
        NextStep();
    }
}

void Tutorial::HandleClick(Vector2 mousePos) {
    if (!isActive) return;
    
    if (currentStep == TutorialStep::PRACTICE_GAME) {
        // Handle practice game clicks
        Vector2 boardPos = {400 - 150, 300 - 150}; // Assuming screen center
        Vector2 boardSize = {300, 300};
        
        // Simple cell calculation (this should match UI implementation)
        float cellWidth = boardSize.x / 3.0f;
        float cellHeight = boardSize.y / 3.0f;
        
        if (mousePos.x >= boardPos.x && mousePos.x <= boardPos.x + boardSize.x &&
            mousePos.y >= boardPos.y && mousePos.y <= boardPos.y + boardSize.y) {
            
            int col = static_cast<int>((mousePos.x - boardPos.x) / cellWidth);
            int row = static_cast<int>((mousePos.y - boardPos.y) / cellHeight);
            
            if (practiceGame.GetCurrentPlayer() == Player::X && !practiceGame.IsGameOver()) {
                if (practiceGame.MakeMove(row, col)) {
                    // Simple AI for practice (random move)
                    if (!practiceGame.IsGameOver()) {
                        std::vector<Move> moves = practiceGame.GetAvailableMoves();
                        if (!moves.empty()) {
                            Move aiMove = moves[0]; // Simple: take first available
                            practiceGame.MakeMove(aiMove.row, aiMove.col);
                        }
                    }
                }
            }
        }
    } else if (waitingForInput) {
        NextStep();
    }
}

TutorialMessage Tutorial::GetCurrentMessage() const {
    if (currentMessageIndex >= 0 && currentMessageIndex < static_cast<int>(messages.size())) {
        return messages[currentMessageIndex];
    }
    return messages[0];
}

void Tutorial::SetupPracticeGame() {
    practiceGame.Reset();
}

void Tutorial::Draw(Vector2 boardPos, Vector2 boardSize) {
    if (!isActive) return;
    
    TutorialMessage msg = GetCurrentMessage();
    
    // Draw semi-transparent overlay
    DrawRectangle(0, 0, GetScreenWidth(), GetScreenHeight(), {0, 0, 0, 150});
    
    // Draw message box
    Rectangle msgBox = {50, 50, GetScreenWidth() - 100, 200};
    DrawRectangleRec(msgBox, {40, 40, 60, 240});
    DrawRectangleLinesEx(msgBox, 2, {100, 100, 150, 255});
    
    // Draw title
    DrawText(msg.title.c_str(), static_cast<int>(msgBox.x + 20), static_cast<int>(msgBox.y + 20), 24, WHITE);
    
    // Draw content
    DrawText(msg.content.c_str(), static_cast<int>(msgBox.x + 20), static_cast<int>(msgBox.y + 60), 16, {200, 200, 200, 255});
    
    // Draw highlight if needed
    if (msg.showHighlight) {
        DrawRectangleLinesEx({boardPos.x - 5, boardPos.y - 5, boardSize.x + 10, boardSize.y + 10}, 
                           3, {255, 255, 100, 255});
    }
    
    // Draw progress indicator
    std::string progress = "Step " + std::to_string(static_cast<int>(currentStep) + 1) + " of " + 
                          std::to_string(static_cast<int>(TutorialStep::COMPLETED));
    DrawText(progress.c_str(), GetScreenWidth() - 150, GetScreenHeight() - 30, 16, {150, 150, 150, 255});
}
