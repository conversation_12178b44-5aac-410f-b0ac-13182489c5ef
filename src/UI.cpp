#include "UI.h"
#include <cmath>

UI::UI() : fontLoaded(false) {}

UI::~UI() {
    if (fontLoaded) {
        UnloadFont(font);
    }
}

void UI::Initialize() {
    // Try to load a custom font, fall back to default if not available
    font = LoadFont("resources/font.ttf");
    if (font.texture.id == 0) {
        font = GetFontDefault();
        fontLoaded = false;
    } else {
        fontLoaded = true;
    }
}

void UI::Update(Vector2 mousePos, bool mousePressed) {
    for (auto& button : buttons) {
        UpdateButton(button, mousePos, mousePressed);
    }
}

void UI::UpdateButton(Button& button, Vector2 mousePos, bool mousePressed) {
    if (!button.isEnabled) {
        button.isHovered = false;
        button.isPressed = false;
        return;
    }
    
    bool wasHovered = button.isHovered;
    button.isHovered = CheckCollisionPointRec(mousePos, button.bounds);
    
    if (button.isHovered && mousePressed) {
        button.isPressed = true;
        if (button.onClick) {
            button.onClick();
        }
    } else {
        button.isPressed = false;
    }
}

void UI::ClearButtons() {
    buttons.clear();
}

void UI::AddButton(Rectangle bounds, const std::string& text, std::function<void()> onClick) {
    buttons.emplace_back(bounds, text, onClick);
}

void UI::DrawButton(const Button& button, const Theme& theme) {
    Color buttonColor = theme.buttonNormal;
    
    if (!button.isEnabled) {
        buttonColor = {buttonColor.r / 2, buttonColor.g / 2, buttonColor.b / 2, buttonColor.a};
    } else if (button.isPressed) {
        buttonColor = theme.buttonPressed;
    } else if (button.isHovered) {
        buttonColor = theme.buttonHover;
    }
    
    // Draw button background with rounded corners effect
    DrawRectangleRec(button.bounds, buttonColor);
    
    // Draw button border
    Color borderColor = button.isHovered ? theme.accent : theme.gridLines;
    DrawRectangleLinesEx(button.bounds, 2, borderColor);
    
    // Draw button text
    Vector2 textSize = MeasureText(button.text, 20);
    Vector2 textPos = {
        button.bounds.x + (button.bounds.width - textSize.x) / 2,
        button.bounds.y + (button.bounds.height - textSize.y) / 2
    };
    
    Color textColor = button.isEnabled ? theme.text : 
                     Color{theme.text.r / 2, theme.text.g / 2, theme.text.b / 2, theme.text.a};
    
    DrawText(button.text, textPos, 20, textColor);
}

void UI::DrawAllButtons(const Theme& theme) {
    for (const auto& button : buttons) {
        DrawButton(button, theme);
    }
}

void UI::DrawText(const std::string& text, Vector2 position, int fontSize, Color color) {
    if (fontLoaded) {
        DrawTextEx(font, text.c_str(), position, static_cast<float>(fontSize), 1.0f, color);
    } else {
        DrawText(text.c_str(), static_cast<int>(position.x), static_cast<int>(position.y), fontSize, color);
    }
}

void UI::DrawTextCentered(const std::string& text, Vector2 center, int fontSize, Color color) {
    Vector2 textSize = MeasureText(text, fontSize);
    Vector2 position = {center.x - textSize.x / 2, center.y - textSize.y / 2};
    DrawText(text, position, fontSize, color);
}

Vector2 UI::MeasureText(const std::string& text, int fontSize) {
    if (fontLoaded) {
        return MeasureTextEx(font, text.c_str(), static_cast<float>(fontSize), 1.0f);
    } else {
        return {static_cast<float>(::MeasureText(text.c_str(), fontSize)), static_cast<float>(fontSize)};
    }
}

void UI::DrawBoard(Vector2 position, Vector2 size, const Theme& theme) {
    // Draw board background
    DrawRectangle(static_cast<int>(position.x), static_cast<int>(position.y), 
                 static_cast<int>(size.x), static_cast<int>(size.y), theme.boardColor);
    
    // Draw grid lines
    float cellWidth = size.x / 3.0f;
    float cellHeight = size.y / 3.0f;
    
    // Vertical lines
    for (int i = 1; i < 3; ++i) {
        float x = position.x + i * cellWidth;
        DrawLineEx({x, position.y}, {x, position.y + size.y}, 3, theme.gridLines);
    }
    
    // Horizontal lines
    for (int i = 1; i < 3; ++i) {
        float y = position.y + i * cellHeight;
        DrawLineEx({position.x, y}, {position.x + size.x, y}, 3, theme.gridLines);
    }
    
    // Draw board border
    DrawRectangleLinesEx({position.x, position.y, size.x, size.y}, 4, theme.gridLines);
}

void UI::DrawCell(Vector2 position, Vector2 size, Player player, const Theme& theme, float alpha) {
    if (player == Player::NONE) return;
    
    Color playerColor = (player == Player::X) ? theme.playerX : theme.playerO;
    playerColor.a = static_cast<unsigned char>(playerColor.a * alpha);
    
    Vector2 center = {position.x + size.x / 2, position.y + size.y / 2};
    float radius = std::min(size.x, size.y) * 0.3f;
    
    if (player == Player::X) {
        // Draw X
        float offset = radius * 0.7f;
        DrawLineEx({center.x - offset, center.y - offset}, 
                  {center.x + offset, center.y + offset}, 6, playerColor);
        DrawLineEx({center.x + offset, center.y - offset}, 
                  {center.x - offset, center.y + offset}, 6, playerColor);
    } else {
        // Draw O
        DrawCircleLines(static_cast<int>(center.x), static_cast<int>(center.y), 
                       radius, playerColor);
        DrawCircleLines(static_cast<int>(center.x), static_cast<int>(center.y), 
                       radius - 1, playerColor);
        DrawCircleLines(static_cast<int>(center.x), static_cast<int>(center.y), 
                       radius - 2, playerColor);
    }
}

void UI::DrawWinLine(const std::vector<Vector2>& winningLine, Vector2 boardPos, Vector2 boardSize, const Theme& theme) {
    if (winningLine.size() != 2) return;
    
    float cellWidth = boardSize.x / 3.0f;
    float cellHeight = boardSize.y / 3.0f;
    
    Vector2 start = {
        boardPos.x + winningLine[0].y * cellWidth + cellWidth / 2,
        boardPos.y + winningLine[0].x * cellHeight + cellHeight / 2
    };
    
    Vector2 end = {
        boardPos.x + winningLine[1].y * cellWidth + cellWidth / 2,
        boardPos.y + winningLine[1].x * cellHeight + cellHeight / 2
    };
    
    DrawLineEx(start, end, 8, theme.highlight);
}

Rectangle UI::GetCellBounds(int row, int col, Vector2 boardPos, Vector2 boardSize) {
    float cellWidth = boardSize.x / 3.0f;
    float cellHeight = boardSize.y / 3.0f;
    
    return {
        boardPos.x + col * cellWidth,
        boardPos.y + row * cellHeight,
        cellWidth,
        cellHeight
    };
}

Vector2 UI::GetCellFromPosition(Vector2 mousePos, Vector2 boardPos, Vector2 boardSize) {
    if (mousePos.x < boardPos.x || mousePos.x > boardPos.x + boardSize.x ||
        mousePos.y < boardPos.y || mousePos.y > boardPos.y + boardSize.y) {
        return {-1, -1};
    }
    
    float cellWidth = boardSize.x / 3.0f;
    float cellHeight = boardSize.y / 3.0f;
    
    int col = static_cast<int>((mousePos.x - boardPos.x) / cellWidth);
    int row = static_cast<int>((mousePos.y - boardPos.y) / cellHeight);
    
    return {static_cast<float>(col), static_cast<float>(row)};
}
