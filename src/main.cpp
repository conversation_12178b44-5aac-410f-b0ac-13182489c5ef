#include "Game.h"
#include <iostream>
#include <exception>

int main() {
    try {
        Game game;
        game.Initialize();
        game.Run();
        game.Shutdown();
        
        std::cout << "Game exited successfully!" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return -1;
    } catch (...) {
        std::cerr << "Unknown error occurred!" << std::endl;
        return -1;
    }
}
