#include "Bot.h"
#include <algorithm>
#include <limits>
#include <chrono>

Bot::Bot() : difficulty(BotDifficulty::MEDIUM), botPlayer(Player::O) {
    auto seed = std::chrono::high_resolution_clock::now().time_since_epoch().count();
    rng.seed(static_cast<unsigned int>(seed));
}

Move Bot::GetBestMove(const GameState& state) {
    switch (difficulty) {
        case BotDifficulty::EASY:
            return GetEasyMove(state);
        case BotDifficulty::MEDIUM:
            return GetMediumMove(state);
        case BotDifficulty::HARD:
            return GetHardMove(state);
        case BotDifficulty::IMPOSSIBLE:
            return GetImpossibleMove(state);
        default:
            return GetMediumMove(state);
    }
}

Move Bot::GetRandomMove(const GameState& state) {
    std::vector<Move> availableMoves = state.GetAvailableMoves();
    if (availableMoves.empty()) {
        return Move();
    }
    
    std::uniform_int_distribution<int> dist(0, static_cast<int>(availableMoves.size()) - 1);
    return availableMoves[dist(rng)];
}

Move Bot::GetEasyMove(const GameState& state) {
    // 70% random, 30% optimal
    std::uniform_int_distribution<int> dist(0, 99);
    if (dist(rng) < 70) {
        return GetRandomMove(state);
    } else {
        return GetImpossibleMove(state);
    }
}

Move Bot::GetMediumMove(const GameState& state) {
    // 40% random, 60% optimal
    std::uniform_int_distribution<int> dist(0, 99);
    if (dist(rng) < 40) {
        return GetRandomMove(state);
    } else {
        return GetImpossibleMove(state);
    }
}

Move Bot::GetHardMove(const GameState& state) {
    // 15% random, 85% optimal
    std::uniform_int_distribution<int> dist(0, 99);
    if (dist(rng) < 15) {
        return GetRandomMove(state);
    } else {
        return GetImpossibleMove(state);
    }
}

Move Bot::GetImpossibleMove(const GameState& state) {
    std::vector<Move> availableMoves = state.GetAvailableMoves();
    if (availableMoves.empty()) {
        return Move();
    }
    
    Move bestMove;
    int bestScore = std::numeric_limits<int>::min();
    
    for (const Move& move : availableMoves) {
        GameState tempState = state.GetCopy();
        tempState.MakeMove(move.row, move.col);
        
        int score = Minimax(tempState, 9, false, std::numeric_limits<int>::min(), std::numeric_limits<int>::max());
        
        if (score > bestScore) {
            bestScore = score;
            bestMove = move;
        }
    }
    
    return bestMove;
}

int Bot::Minimax(GameState& state, int depth, bool isMaximizing, int alpha, int beta) {
    if (depth == 0 || state.IsGameOver()) {
        return EvaluateBoard(state);
    }
    
    std::vector<Move> availableMoves = state.GetAvailableMoves();
    
    if (isMaximizing) {
        int maxEval = std::numeric_limits<int>::min();
        
        for (const Move& move : availableMoves) {
            state.MakeMove(move.row, move.col);
            int eval = Minimax(state, depth - 1, false, alpha, beta);
            state.UndoMove();
            
            maxEval = std::max(maxEval, eval);
            alpha = std::max(alpha, eval);
            
            if (beta <= alpha) {
                break; // Alpha-beta pruning
            }
        }
        
        return maxEval;
    } else {
        int minEval = std::numeric_limits<int>::max();
        
        for (const Move& move : availableMoves) {
            state.MakeMove(move.row, move.col);
            int eval = Minimax(state, depth - 1, true, alpha, beta);
            state.UndoMove();
            
            minEval = std::min(minEval, eval);
            beta = std::min(beta, eval);
            
            if (beta <= alpha) {
                break; // Alpha-beta pruning
            }
        }
        
        return minEval;
    }
}

int Bot::EvaluateBoard(const GameState& state) {
    GameResult result = state.GetResult();
    
    if (result == GameResult::DRAW) {
        return 0;
    } else if ((result == GameResult::X_WINS && botPlayer == Player::X) ||
               (result == GameResult::O_WINS && botPlayer == Player::O)) {
        return 10;
    } else if ((result == GameResult::X_WINS && botPlayer == Player::O) ||
               (result == GameResult::O_WINS && botPlayer == Player::X)) {
        return -10;
    }
    
    // Game is ongoing, return neutral score
    return 0;
}

std::string Bot::GetDifficultyName() const {
    switch (difficulty) {
        case BotDifficulty::EASY:
            return "Easy";
        case BotDifficulty::MEDIUM:
            return "Medium";
        case BotDifficulty::HARD:
            return "Hard";
        case BotDifficulty::IMPOSSIBLE:
            return "Impossible";
        default:
            return "Medium";
    }
}

std::vector<std::string> Bot::GetDifficultyNames() {
    return {"Easy", "Medium", "Hard", "Impossible"};
}
