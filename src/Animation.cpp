#include "Animation.h"
#include <cmath>
#include <algorithm>

Animation::Animation() {}

void Animation::Update(float deltaTime) {
    for (auto& anim : animations) {
        if (!anim.isActive) continue;
        
        anim.elapsed += deltaTime;
        
        if (anim.elapsed >= anim.duration) {
            anim.elapsed = anim.duration;
            anim.currentValue = anim.endValue;
            
            if (anim.onUpdate) {
                anim.onUpdate(anim.currentValue);
            }
            
            if (anim.onComplete) {
                anim.onComplete();
            }
            
            if (anim.loop) {
                anim.elapsed = 0.0f;
                anim.currentValue = anim.startValue;
            } else {
                anim.isActive = false;
            }
        } else {
            float t = anim.elapsed / anim.duration;
            t = ApplyEasing(t, anim.easeType);
            anim.currentValue = anim.startValue + (anim.endValue - anim.startValue) * t;
            
            if (anim.onUpdate) {
                anim.onUpdate(anim.currentValue);
            }
        }
    }
}

int Animation::StartAnimation(float startValue, float endValue, float duration, 
                            EaseType easeType, std::function<void(float)> onUpdate,
                            std::function<void()> onComplete, bool loop) {
    AnimationData anim;
    anim.startValue = startValue;
    anim.endValue = endValue;
    anim.currentValue = startValue;
    anim.duration = duration;
    anim.elapsed = 0.0f;
    anim.easeType = easeType;
    anim.onUpdate = onUpdate;
    anim.onComplete = onComplete;
    anim.isActive = true;
    anim.loop = loop;
    
    animations.push_back(anim);
    return static_cast<int>(animations.size()) - 1;
}

void Animation::StopAnimation(int id) {
    if (id >= 0 && id < static_cast<int>(animations.size())) {
        animations[id].isActive = false;
    }
}

void Animation::StopAllAnimations() {
    for (auto& anim : animations) {
        anim.isActive = false;
    }
}

bool Animation::IsAnimationActive(int id) {
    if (id >= 0 && id < static_cast<int>(animations.size())) {
        return animations[id].isActive;
    }
    return false;
}

int Animation::GetActiveAnimationCount() {
    int count = 0;
    for (const auto& anim : animations) {
        if (anim.isActive) count++;
    }
    return count;
}

float Animation::ApplyEasing(float t, EaseType easeType) {
    switch (easeType) {
        case EaseType::LINEAR:
            return t;
            
        case EaseType::EASE_IN:
            return t * t;
            
        case EaseType::EASE_OUT:
            return 1.0f - (1.0f - t) * (1.0f - t);
            
        case EaseType::EASE_IN_OUT:
            return t < 0.5f ? 2.0f * t * t : 1.0f - 2.0f * (1.0f - t) * (1.0f - t);
            
        case EaseType::BOUNCE:
            if (t < 1.0f / 2.75f) {
                return 7.5625f * t * t;
            } else if (t < 2.0f / 2.75f) {
                t -= 1.5f / 2.75f;
                return 7.5625f * t * t + 0.75f;
            } else if (t < 2.5f / 2.75f) {
                t -= 2.25f / 2.75f;
                return 7.5625f * t * t + 0.9375f;
            } else {
                t -= 2.625f / 2.75f;
                return 7.5625f * t * t + 0.984375f;
            }
            
        case EaseType::ELASTIC:
            if (t == 0.0f) return 0.0f;
            if (t == 1.0f) return 1.0f;
            
            float p = 0.3f;
            float s = p / 4.0f;
            return std::pow(2.0f, -10.0f * t) * std::sin((t - s) * (2.0f * 3.14159f) / p) + 1.0f;
            
        default:
            return t;
    }
}

// Predefined animations
int Animation::FadeIn(float duration, std::function<void(float)> onUpdate, std::function<void()> onComplete) {
    return StartAnimation(0.0f, 1.0f, duration, EaseType::EASE_OUT, onUpdate, onComplete);
}

int Animation::FadeOut(float duration, std::function<void(float)> onUpdate, std::function<void()> onComplete) {
    return StartAnimation(1.0f, 0.0f, duration, EaseType::EASE_IN, onUpdate, onComplete);
}

int Animation::ScaleUp(float duration, std::function<void(float)> onUpdate, std::function<void()> onComplete) {
    return StartAnimation(0.0f, 1.0f, duration, EaseType::BOUNCE, onUpdate, onComplete);
}

int Animation::ScaleDown(float duration, std::function<void(float)> onUpdate, std::function<void()> onComplete) {
    return StartAnimation(1.0f, 0.0f, duration, EaseType::EASE_IN, onUpdate, onComplete);
}

int Animation::SlideIn(float startX, float endX, float duration, std::function<void(float)> onUpdate, std::function<void()> onComplete) {
    return StartAnimation(startX, endX, duration, EaseType::EASE_OUT, onUpdate, onComplete);
}
