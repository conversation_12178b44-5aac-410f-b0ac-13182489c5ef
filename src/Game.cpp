#include "Game.h"
#include <iostream>

Game::Game() : currentMode(GameMode::MENU), isRunning(false), deltaTime(0.0f) {
    gameState = std::make_unique<GameState>();
    themeManager = std::make_unique<ThemeManager>();
    bot = std::make_unique<Bot>();
    tutorial = std::make_unique<Tutorial>();
    ui = std::make_unique<UI>();
    animation = std::make_unique<Animation>();
}

Game::~Game() {
    Shutdown();
}

void Game::Initialize() {
    InitWindow(SCREEN_WIDTH, SCREEN_HEIGHT, "Raylib Tic-Tac-Toe");
    SetTargetFPS(60);
    
    ui->Initialize();
    isRunning = true;
    
    std::cout << "Game initialized successfully!" << std::endl;
}

void Game::Run() {
    while (isRunning && !WindowShouldClose()) {
        deltaTime = GetFrameTime();
        
        HandleInput();
        Update();
        
        BeginDrawing();
        Draw();
        EndDrawing();
    }
}

void Game::Shutdown() {
    if (IsWindowReady()) {
        CloseWindow();
    }
    isRunning = false;
}

void Game::SetGameMode(GameMode mode) {
    currentMode = mode;
    ui->ClearButtons();
    
    if (mode == GameMode::PLAYER_VS_PLAYER || mode == GameMode::PLAYER_VS_BOT) {
        gameState->Reset();
        if (mode == GameMode::PLAYER_VS_BOT) {
            bot->SetPlayer(Player::O);
        }
    } else if (mode == GameMode::TUTORIAL) {
        tutorial->Start();
    }
}

void Game::HandleInput() {
    Vector2 mousePos = GetMousePosition();
    bool mousePressed = IsMouseButtonPressed(MOUSE_BUTTON_LEFT);
    
    ui->Update(mousePos, mousePressed);
    
    if (currentMode == GameMode::TUTORIAL) {
        if (mousePressed) {
            tutorial->HandleClick(mousePos);
        }
        if (IsKeyPressed(KEY_ESCAPE)) {
            tutorial->Stop();
            SetGameMode(GameMode::MENU);
        }
    } else if (currentMode == GameMode::PLAYER_VS_PLAYER || currentMode == GameMode::PLAYER_VS_BOT) {
        if (IsKeyPressed(KEY_ESCAPE)) {
            SetGameMode(GameMode::MENU);
        }
        if (IsKeyPressed(KEY_R)) {
            gameState->Reset();
        }
        
        if (mousePressed && !gameState->IsGameOver()) {
            Vector2 boardPos = {SCREEN_WIDTH / 2.0f - 150, SCREEN_HEIGHT / 2.0f - 150};
            Vector2 boardSize = {300, 300};
            Vector2 cellPos = ui->GetCellFromPosition(mousePos, boardPos, boardSize);
            
            if (cellPos.x >= 0 && cellPos.y >= 0) {
                int row = static_cast<int>(cellPos.y);
                int col = static_cast<int>(cellPos.x);
                
                if (currentMode == GameMode::PLAYER_VS_PLAYER) {
                    gameState->MakeMove(row, col);
                } else if (currentMode == GameMode::PLAYER_VS_BOT && gameState->GetCurrentPlayer() == Player::X) {
                    if (gameState->MakeMove(row, col)) {
                        // Bot makes move after player
                        if (!gameState->IsGameOver()) {
                            Move botMove = bot->GetBestMove(*gameState);
                            if (botMove.row >= 0 && botMove.col >= 0) {
                                gameState->MakeMove(botMove.row, botMove.col);
                            }
                        }
                    }
                }
            }
        }
    } else if (currentMode == GameMode::SETTINGS) {
        if (IsKeyPressed(KEY_ESCAPE)) {
            SetGameMode(GameMode::MENU);
        }
    }
}

void Game::Update() {
    themeManager->Update(deltaTime);
    animation->Update(deltaTime);

    if (currentMode == GameMode::TUTORIAL) {
        tutorial->Update(deltaTime);
    }
}

void Game::Draw() {
    Theme theme = themeManager->GetCurrentTheme();
    ClearBackground(theme.background);

    switch (currentMode) {
        case GameMode::MENU:
            DrawMenu();
            break;
        case GameMode::PLAYER_VS_PLAYER:
        case GameMode::PLAYER_VS_BOT:
            DrawGame();
            break;
        case GameMode::TUTORIAL:
            DrawGame();
            tutorial->Draw({SCREEN_WIDTH / 2.0f - 150, SCREEN_HEIGHT / 2.0f - 150}, {300, 300});
            break;
        case GameMode::SETTINGS:
            DrawSettings();
            break;
    }
}

void Game::DrawMenu() {
    Theme theme = themeManager->GetCurrentTheme();

    // Title
    ui->DrawTextCentered("TIC-TAC-TOE", {SCREEN_WIDTH / 2.0f, 100}, 48, theme.text);

    // Menu buttons
    float buttonWidth = 200;
    float buttonHeight = 50;
    float buttonSpacing = 20;
    float startY = SCREEN_HEIGHT / 2.0f - 100;

    ui->ClearButtons();

    ui->AddButton({SCREEN_WIDTH / 2.0f - buttonWidth / 2, startY, buttonWidth, buttonHeight},
                  "Player vs Player", [this]() { SetGameMode(GameMode::PLAYER_VS_PLAYER); });

    ui->AddButton({SCREEN_WIDTH / 2.0f - buttonWidth / 2, startY + buttonHeight + buttonSpacing, buttonWidth, buttonHeight},
                  "Player vs Bot", [this]() { SetGameMode(GameMode::PLAYER_VS_BOT); });

    ui->AddButton({SCREEN_WIDTH / 2.0f - buttonWidth / 2, startY + 2 * (buttonHeight + buttonSpacing), buttonWidth, buttonHeight},
                  "Tutorial", [this]() { SetGameMode(GameMode::TUTORIAL); });

    ui->AddButton({SCREEN_WIDTH / 2.0f - buttonWidth / 2, startY + 3 * (buttonHeight + buttonSpacing), buttonWidth, buttonHeight},
                  "Settings", [this]() { SetGameMode(GameMode::SETTINGS); });

    ui->DrawAllButtons(theme);

    // Theme indicator
    ui->DrawTextCentered("Theme: " + themeManager->GetCurrentThemeName(),
                        {SCREEN_WIDTH / 2.0f, SCREEN_HEIGHT - 50}, 20, theme.text);
}

void Game::DrawGame() {
    Theme theme = themeManager->GetCurrentTheme();
    Vector2 boardPos = {SCREEN_WIDTH / 2.0f - 150, SCREEN_HEIGHT / 2.0f - 150};
    Vector2 boardSize = {300, 300};

    // Draw board
    ui->DrawBoard(boardPos, boardSize, theme);

    // Draw cells
    for (int row = 0; row < 3; ++row) {
        for (int col = 0; col < 3; ++col) {
            Player player = gameState->GetCell(row, col);
            if (player != Player::NONE) {
                Rectangle cellBounds = ui->GetCellBounds(row, col, boardPos, boardSize);
                ui->DrawCell({cellBounds.x, cellBounds.y}, {cellBounds.width, cellBounds.height},
                           player, theme);
            }
        }
    }

    // Draw winning line
    if (gameState->IsGameOver() && gameState->GetResult() != GameResult::DRAW) {
        ui->DrawWinLine(gameState->GetWinningLine(), boardPos, boardSize, theme);
    }

    // Game status
    std::string statusText;
    if (gameState->IsGameOver()) {
        switch (gameState->GetResult()) {
            case GameResult::X_WINS:
                statusText = "X Wins!";
                break;
            case GameResult::O_WINS:
                statusText = "O Wins!";
                break;
            case GameResult::DRAW:
                statusText = "It's a Draw!";
                break;
            default:
                break;
        }
    } else {
        statusText = "Current Player: " + std::string(gameState->GetCurrentPlayer() == Player::X ? "X" : "O");
    }

    ui->DrawTextCentered(statusText, {SCREEN_WIDTH / 2.0f, 50}, 24, theme.text);

    // Instructions
    ui->DrawTextCentered("Press R to restart, ESC for menu", {SCREEN_WIDTH / 2.0f, SCREEN_HEIGHT - 30}, 16, theme.text);
}

void Game::DrawSettings() {
    Theme theme = themeManager->GetCurrentTheme();

    ui->DrawTextCentered("SETTINGS", {SCREEN_WIDTH / 2.0f, 100}, 36, theme.text);

    // Theme selection
    ui->DrawTextCentered("Themes", {SCREEN_WIDTH / 2.0f, 200}, 24, theme.text);

    float buttonWidth = 150;
    float buttonHeight = 40;
    float buttonSpacing = 10;
    int themesPerRow = 3;

    ui->ClearButtons();

    std::vector<std::string> themeNames = themeManager->GetThemeNames();
    for (int i = 0; i < static_cast<int>(themeNames.size()); ++i) {
        int row = i / themesPerRow;
        int col = i % themesPerRow;

        float x = SCREEN_WIDTH / 2.0f - (themesPerRow * buttonWidth + (themesPerRow - 1) * buttonSpacing) / 2.0f +
                  col * (buttonWidth + buttonSpacing);
        float y = 250 + row * (buttonHeight + buttonSpacing);

        ui->AddButton({x, y, buttonWidth, buttonHeight}, themeNames[i],
                     [this, i]() { themeManager->SetTheme(i); });
    }

    // Back button
    ui->AddButton({SCREEN_WIDTH / 2.0f - 75, SCREEN_HEIGHT - 100, 150, 50},
                  "Back", [this]() { SetGameMode(GameMode::MENU); });

    ui->DrawAllButtons(theme);

    ui->DrawTextCentered("Press ESC to go back", {SCREEN_WIDTH / 2.0f, SCREEN_HEIGHT - 30}, 16, theme.text);
}
