#include "ThemeManager.h"
#include <cmath>

ThemeManager::ThemeManager() : currentThemeIndex(0), transitionProgress(0.0f), isTransitioning(false) {
    InitializeThemes();
}

void ThemeManager::InitializeThemes() {
    // Theme 1: Classic Blue
    themes.push_back({
        "Classic Blue",
        {30, 30, 50, 255},      // background
        {50, 50, 80, 255},      // boardColor
        {100, 100, 150, 255},   // gridLines
        {255, 100, 100, 255},   // playerX (red)
        {100, 150, 255, 255},   // playerO (blue)
        {255, 255, 100, 255},   // highlight (yellow)
        {255, 255, 255, 255},   // text (white)
        {70, 70, 100, 255},     // buttonNormal
        {90, 90, 120, 255},     // buttonHover
        {50, 50, 70, 255},      // buttonPressed
        {150, 150, 255, 255}    // accent
    });
    
    // Theme 2: Forest Green
    themes.push_back({
        "Forest Green",
        {20, 40, 20, 255},      // background
        {40, 60, 40, 255},      // boardColor
        {80, 120, 80, 255},     // gridLines
        {255, 150, 100, 255},   // playerX (orange)
        {150, 255, 150, 255},   // playerO (light green)
        {255, 255, 150, 255},   // highlight
        {240, 255, 240, 255},   // text
        {60, 80, 60, 255},      // buttonNormal
        {80, 100, 80, 255},     // buttonHover
        {40, 60, 40, 255},      // buttonPressed
        {150, 255, 150, 255}    // accent
    });
    
    // Theme 3: Sunset Orange
    themes.push_back({
        "Sunset Orange",
        {60, 30, 20, 255},      // background
        {80, 50, 40, 255},      // boardColor
        {150, 100, 80, 255},    // gridLines
        {255, 200, 100, 255},   // playerX (yellow)
        {255, 100, 150, 255},   // playerO (pink)
        {255, 255, 200, 255},   // highlight
        {255, 240, 220, 255},   // text
        {100, 70, 60, 255},     // buttonNormal
        {120, 90, 80, 255},     // buttonHover
        {80, 50, 40, 255},      // buttonPressed
        {255, 150, 100, 255}    // accent
    });
    
    // Theme 4: Ocean Deep
    themes.push_back({
        "Ocean Deep",
        {10, 25, 40, 255},      // background
        {20, 40, 60, 255},      // boardColor
        {50, 100, 150, 255},    // gridLines
        {255, 180, 100, 255},   // playerX (coral)
        {100, 200, 255, 255},   // playerO (cyan)
        {150, 255, 255, 255},   // highlight
        {200, 240, 255, 255},   // text
        {30, 60, 90, 255},      // buttonNormal
        {50, 80, 110, 255},     // buttonHover
        {20, 40, 70, 255},      // buttonPressed
        {100, 200, 255, 255}    // accent
    });
    
    // Theme 5: Purple Neon
    themes.push_back({
        "Purple Neon",
        {25, 10, 40, 255},      // background
        {45, 20, 60, 255},      // boardColor
        {100, 50, 150, 255},    // gridLines
        {255, 100, 255, 255},   // playerX (magenta)
        {150, 255, 200, 255},   // playerO (mint)
        {255, 200, 255, 255},   // highlight
        {255, 200, 255, 255},   // text
        {65, 30, 80, 255},      // buttonNormal
        {85, 50, 100, 255},     // buttonHover
        {45, 20, 60, 255},      // buttonPressed
        {200, 100, 255, 255}    // accent
    });
    
    // Theme 6: Monochrome
    themes.push_back({
        "Monochrome",
        {20, 20, 20, 255},      // background
        {40, 40, 40, 255},      // boardColor
        {100, 100, 100, 255},   // gridLines
        {255, 255, 255, 255},   // playerX (white)
        {150, 150, 150, 255},   // playerO (gray)
        {200, 200, 200, 255},   // highlight
        {255, 255, 255, 255},   // text
        {60, 60, 60, 255},      // buttonNormal
        {80, 80, 80, 255},      // buttonHover
        {40, 40, 40, 255},      // buttonPressed
        {180, 180, 180, 255}    // accent
    });
}

void ThemeManager::Update(float deltaTime) {
    if (isTransitioning) {
        transitionProgress += deltaTime * 3.0f; // 3 seconds transition
        
        if (transitionProgress >= 1.0f) {
            transitionProgress = 1.0f;
            isTransitioning = false;
        }
    }
}

void ThemeManager::SetTheme(int index) {
    if (index < 0 || index >= static_cast<int>(themes.size()) || index == currentThemeIndex) {
        return;
    }
    
    if (!isTransitioning) {
        transitionFrom = GetCurrentTheme();
        transitionTo = themes[index];
        transitionProgress = 0.0f;
        isTransitioning = true;
    }
    
    currentThemeIndex = index;
}

void ThemeManager::NextTheme() {
    SetTheme((currentThemeIndex + 1) % static_cast<int>(themes.size()));
}

void ThemeManager::PreviousTheme() {
    SetTheme((currentThemeIndex - 1 + static_cast<int>(themes.size())) % static_cast<int>(themes.size()));
}

Theme ThemeManager::GetCurrentTheme() {
    if (!isTransitioning) {
        return themes[currentThemeIndex];
    }
    
    // Smooth transition between themes
    Theme result;
    result.name = themes[currentThemeIndex].name;
    result.background = LerpColor(transitionFrom.background, transitionTo.background, transitionProgress);
    result.boardColor = LerpColor(transitionFrom.boardColor, transitionTo.boardColor, transitionProgress);
    result.gridLines = LerpColor(transitionFrom.gridLines, transitionTo.gridLines, transitionProgress);
    result.playerX = LerpColor(transitionFrom.playerX, transitionTo.playerX, transitionProgress);
    result.playerO = LerpColor(transitionFrom.playerO, transitionTo.playerO, transitionProgress);
    result.highlight = LerpColor(transitionFrom.highlight, transitionTo.highlight, transitionProgress);
    result.text = LerpColor(transitionFrom.text, transitionTo.text, transitionProgress);
    result.buttonNormal = LerpColor(transitionFrom.buttonNormal, transitionTo.buttonNormal, transitionProgress);
    result.buttonHover = LerpColor(transitionFrom.buttonHover, transitionTo.buttonHover, transitionProgress);
    result.buttonPressed = LerpColor(transitionFrom.buttonPressed, transitionTo.buttonPressed, transitionProgress);
    result.accent = LerpColor(transitionFrom.accent, transitionTo.accent, transitionProgress);
    
    return result;
}

std::string ThemeManager::GetCurrentThemeName() {
    return themes[currentThemeIndex].name;
}

std::vector<std::string> ThemeManager::GetThemeNames() {
    std::vector<std::string> names;
    for (const auto& theme : themes) {
        names.push_back(theme.name);
    }
    return names;
}

Color ThemeManager::LerpColor(Color from, Color to, float t) {
    // Smooth easing function
    t = t * t * (3.0f - 2.0f * t); // smoothstep
    
    return {
        static_cast<unsigned char>(from.r + (to.r - from.r) * t),
        static_cast<unsigned char>(from.g + (to.g - from.g) * t),
        static_cast<unsigned char>(from.b + (to.b - from.b) * t),
        static_cast<unsigned char>(from.a + (to.a - from.a) * t)
    };
}
