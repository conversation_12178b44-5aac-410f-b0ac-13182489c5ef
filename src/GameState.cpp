#include "GameState.h"
#include <algorithm>

GameState::GameState() {
    Reset();
}

void GameState::Reset() {
    for (auto& row : board) {
        row.fill(Player::NONE);
    }
    currentPlayer = Player::X;
    result = GameResult::ONGOING;
    moveHistory.clear();
    winningLine.clear();
}

bool GameState::MakeMove(int row, int col) {
    if (!IsValidMove(row, col) || IsGameOver()) {
        return false;
    }
    
    board[row][col] = currentPlayer;
    moveHistory.emplace_back(row, col, currentPlayer);
    
    UpdateGameResult();
    
    if (result == GameResult::ONGOING) {
        currentPlayer = (currentPlayer == Player::X) ? Player::O : Player::X;
    }
    
    return true;
}

bool GameState::UndoMove() {
    if (moveHistory.empty()) {
        return false;
    }
    
    Move lastMove = moveHistory.back();
    moveHistory.pop_back();
    
    board[lastMove.row][lastMove.col] = Player::NONE;
    currentPlayer = lastMove.player;
    result = GameResult::ONGOING;
    winningLine.clear();
    
    return true;
}

Player GameState::GetCell(int row, int col) const {
    if (row < 0 || row >= 3 || col < 0 || col >= 3) {
        return Player::NONE;
    }
    return board[row][col];
}

bool GameState::IsValidMove(int row, int col) const {
    return row >= 0 && row < 3 && col >= 0 && col < 3 && board[row][col] == Player::NONE;
}

bool GameState::IsGameOver() const {
    return result != GameResult::ONGOING;
}

std::vector<Move> GameState::GetAvailableMoves() const {
    std::vector<Move> moves;
    for (int row = 0; row < 3; ++row) {
        for (int col = 0; col < 3; ++col) {
            if (board[row][col] == Player::NONE) {
                moves.emplace_back(row, col, currentPlayer);
            }
        }
    }
    return moves;
}

bool GameState::CheckWin(Player player) {
    // Check rows
    for (int row = 0; row < 3; ++row) {
        if (board[row][0] == player && board[row][1] == player && board[row][2] == player) {
            winningLine = {{static_cast<float>(row), 0}, {static_cast<float>(row), 2}};
            return true;
        }
    }
    
    // Check columns
    for (int col = 0; col < 3; ++col) {
        if (board[0][col] == player && board[1][col] == player && board[2][col] == player) {
            winningLine = {{0, static_cast<float>(col)}, {2, static_cast<float>(col)}};
            return true;
        }
    }
    
    // Check diagonals
    if (board[0][0] == player && board[1][1] == player && board[2][2] == player) {
        winningLine = {{0, 0}, {2, 2}};
        return true;
    }
    
    if (board[0][2] == player && board[1][1] == player && board[2][0] == player) {
        winningLine = {{0, 2}, {2, 0}};
        return true;
    }
    
    return false;
}

bool GameState::CheckDraw() {
    for (const auto& row : board) {
        for (Player cell : row) {
            if (cell == Player::NONE) {
                return false;
            }
        }
    }
    return true;
}

void GameState::UpdateGameResult() {
    if (CheckWin(Player::X)) {
        result = GameResult::X_WINS;
    } else if (CheckWin(Player::O)) {
        result = GameResult::O_WINS;
    } else if (CheckDraw()) {
        result = GameResult::DRAW;
    } else {
        result = GameResult::ONGOING;
    }
}

GameState GameState::GetCopy() const {
    GameState copy;
    copy.board = this->board;
    copy.currentPlayer = this->currentPlayer;
    copy.result = this->result;
    copy.moveHistory = this->moveHistory;
    copy.winningLine = this->winningLine;
    return copy;
}

void GameState::SetBoard(const std::array<std::array<Player, 3>, 3>& newBoard) {
    board = newBoard;
    UpdateGameResult();
}
